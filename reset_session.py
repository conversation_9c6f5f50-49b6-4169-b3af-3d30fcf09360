#!/usr/bin/env python3
"""
أداة إعادة تعيين جلسة البوت
Bot Session Reset Tool

استخدم هذا الملف لحذف ملفات الجلسة في حالة مواجهة مشاكل Rate Limiting
"""

import os
import glob
from pathlib import Path

def reset_sessions():
    """حذف جميع ملفات الجلسة"""
    print("🔄 جاري البحث عن ملفات الجلسة...")
    
    # أنماط ملفات الجلسة
    session_patterns = [
        "*.session",
        "*.session-journal",
        "*bot_session*",
        "enhanced_bot_session*",
        "public_bot_session*"
    ]
    
    deleted_files = []
    
    for pattern in session_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                deleted_files.append(file)
                print(f"✅ تم حذف: {file}")
            except Exception as e:
                print(f"❌ لا يمكن حذف {file}: {e}")
    
    if deleted_files:
        print(f"\n🎉 تم حذف {len(deleted_files)} ملف جلسة بنجاح!")
        print("💡 يمكنك الآن تشغيل البوت مرة أخرى")
    else:
        print("ℹ️ لم يتم العثور على ملفات جلسة للحذف")

def main():
    print("🛠️ أداة إعادة تعيين جلسة البوت")
    print("=" * 40)
    
    response = input("هل تريد حذف جميع ملفات الجلسة؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        reset_sessions()
    else:
        print("❌ تم إلغاء العملية")

if __name__ == '__main__':
    main()
