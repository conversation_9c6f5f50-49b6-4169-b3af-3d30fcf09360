#!/usr/bin/env python3
"""
بوت تيليجرام محسن لنسخ الرسائل
Enhanced Telegram Bot for Message Copying

الإصدار: 2.0
المطور: محسن ومطور
"""

import asyncio
import logging
import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.append(str(Path(__file__).parent / "src"))

from src.bot.telegram_bot import TelegramBot
from src.config.settings import Settings
from src.utils.logger import setup_logger

async def main():
    """دالة رئيسية لتشغيل البوت"""
    try:
        # إعداد التسجيل
        logger = setup_logger()
        logger.info("🚀 بدء تشغيل البوت...")

        # تحميل الإعدادات
        settings = Settings()

        # إنشاء وتشغيل البوت
        bot = TelegramBot(settings)
        await bot.start()

    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())

