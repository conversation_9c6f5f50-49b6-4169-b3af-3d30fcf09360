from telethon import TelegramClient, events, Button
from telethon.tl.types import DocumentAttributeFilename, MessageMediaDocument
import asyncio
import os
import re
import time
from dotenv import load_dotenv

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# إعدادات البوت (مخفية في ملف .env)
try:
    API_ID = int(os.getenv('TELEGRAM_API_ID'))
    API_HASH = os.getenv('TELEGRAM_API_HASH')
    BOT_TOKEN = os.getenv('BOT_TOKEN')

    # إعدادات إضافية محسنة
    MAX_RETRIES = int(os.getenv('BOT_MAX_RETRIES', 3))
    DELAY_BETWEEN_MESSAGES = float(os.getenv('BOT_DELAY_BETWEEN_MESSAGES', 0.5))
    DEFAULT_LIMIT = int(os.getenv('BOT_DEFAULT_LIMIT', 100))
    MAX_FILE_SIZE_MB = int(os.getenv('BOT_MAX_FILE_SIZE_MB', 2000))

    # قائمة المشرفين (اختيارية)
    ADMIN_IDS = []
    admin_ids_str = os.getenv('ADMIN_USER_IDS', '')
    if admin_ids_str:
        ADMIN_IDS = [int(id.strip()) for id in admin_ids_str.split(',') if id.strip()]

except (ValueError, TypeError) as e:
    print(f"❌ خطأ في قراءة الإعدادات: {e}")
    exit(1)

# التحقق من وجود المتغيرات المطلوبة
if not all([API_ID, API_HASH, BOT_TOKEN]):
    print("❌ خطأ: تأكد من وجود جميع المتغيرات المطلوبة في ملف .env")
    print("المتغيرات المطلوبة: TELEGRAM_API_ID, TELEGRAM_API_HASH, BOT_TOKEN")
    exit(1)

# إنشاء مجلد للتحميلات المؤقتة
TEMP_DOWNLOAD_DIR = "temp_downloads"
if not os.path.exists(TEMP_DOWNLOAD_DIR):
    os.makedirs(TEMP_DOWNLOAD_DIR)

# إنشاء العميل - بوت عام مع إعدادات محسنة
session_name = f'enhanced_bot_session_{int(time.time())}'  # جلسة فريدة
client = TelegramClient(session_name, API_ID, API_HASH)

def is_admin(user_id):
    """التحقق من كون المستخدم مشرف"""
    return user_id in ADMIN_IDS

def validate_channel_link(channel_link):
    """التحقق من صحة رابط القناة"""
    patterns = [
        r"https://t\.me/([^/]+)",
        r"https://t\.me/c/(\d+)",
        r"@([a-zA-Z0-9_]+)"
    ]

    for pattern in patterns:
        match = re.match(pattern, channel_link)
        if match:
            return match.group(1)
    return None

def get_file_name(msg):
    """استخراج اسم الملف من الرسالة"""
    if hasattr(msg, 'media') and isinstance(msg.media, MessageMediaDocument):
        for attr in msg.media.document.attributes:
            if isinstance(attr, DocumentAttributeFilename):
                return attr.file_name
    return f"file_{int(time.time())}_{msg.id}"

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f}{size_names[i]}"

async def copy_channel_messages(event, channel_link, limit=None):
    """نسخ رسائل من قناة مع تحسينات"""
    user_id = event.sender_id
    start_time = time.time()

    try:
        # التحقق من صحة الرابط
        channel = validate_channel_link(channel_link)
        if not channel:
            await event.reply("❌ رابط القناة غير صحيح!\n\n"
                            "الأشكال المدعومة:\n"
                            "• https://t.me/channel_name\n"
                            "• https://t.me/c/123456789\n"
                            "• @channel_name")
            return

        # تحويل معرف القناة إذا كان رقمي
        if channel.isdigit():
            channel = int(f"-100{channel}")

        # تحديد الحد الأقصى للرسائل
        if limit is None:
            limit = DEFAULT_LIMIT

        # التحقق من الحد الأقصى للمستخدمين العاديين
        if not is_admin(user_id) and limit > 200:
            limit = 200
            await event.reply("⚠️ تم تقليل العدد إلى 200 رسالة للمستخدمين العاديين")

        # رسالة البداية
        progress_msg = await event.reply(f"🔄 جاري نسخ آخر {limit} رسالة من القناة...")

        # جلب الرسائل
        messages = await client.get_messages(channel, limit=limit, filter=None)

        if not messages:
            await progress_msg.edit("⚠️ لم يتم العثور على رسائل في هذه القناة!")
            return

        # إحصائيات العملية
        copied_count = 0
        failed_count = 0
        total_size = 0

        # معالجة الرسائل
        for i, msg in enumerate(messages[::-1], 1):
            try:
                # تحديث شريط التقدم كل 10 رسائل
                if i % 10 == 0:
                    progress = (i / len(messages)) * 100
                    await progress_msg.edit(f"🔄 جاري النسخ... {progress:.1f}%\n"
                                          f"✅ تم: {copied_count} | ❌ فشل: {failed_count}")

                # معالجة الملفات
                if hasattr(msg, 'media') and isinstance(msg.media, MessageMediaDocument):
                    file_name = get_file_name(msg)
                    file_size = msg.media.document.size

                    # التحقق من حجم الملف
                    if file_size > MAX_FILE_SIZE_MB * 1024 * 1024:
                        print(f"⚠️ تم تخطي ملف كبير: {file_name} ({format_file_size(file_size)})")
                        failed_count += 1
                        continue

                    print(f"⬇️ تحميل: {file_name} ({format_file_size(file_size)})")

                    # تحميل الملف
                    download_path = os.path.join(TEMP_DOWNLOAD_DIR, file_name)
                    await msg.download_media(download_path)

                    # رفع الملف
                    await client.send_file(
                        event.chat_id,
                        download_path,
                        caption=msg.text if msg.text else None,
                        force_document=True
                    )

                    total_size += file_size

                    # حذف الملف المؤقت
                    try:
                        os.remove(download_path)
                    except Exception as cleanup_error:
                        print(f"⚠️ لا يمكن حذف الملف المؤقت: {cleanup_error}")

                # معالجة الرسائل النصية والصور
                else:
                    await client.send_message(
                        event.chat_id,
                        message=msg,
                        formatting_entities=msg.entities
                    )

                copied_count += 1
                await asyncio.sleep(DELAY_BETWEEN_MESSAGES)

            except Exception as msg_error:
                print(f"❌ خطأ في نسخ رسالة {msg.id}: {str(msg_error)}")
                failed_count += 1
                continue

        # تنظيف المجلد المؤقت
        try:
            if os.path.exists(TEMP_DOWNLOAD_DIR) and not os.listdir(TEMP_DOWNLOAD_DIR):
                os.rmdir(TEMP_DOWNLOAD_DIR)
        except Exception as cleanup_error:
            print(f"⚠️ لا يمكن حذف المجلد المؤقت: {cleanup_error}")

        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time

        # رسالة الإنجاز المحسنة
        success_msg = (
            f"✅ **اكتمل النسخ بنجاح!**\n\n"
            f"📊 **الإحصائيات:**\n"
            f"✓ تم نسخ: **{copied_count}** رسالة\n"
            f"❌ فشل نسخ: **{failed_count}** رسالة\n"
            f"📁 إجمالي الحجم: **{format_file_size(total_size)}**\n"
            f"⏱️ الوقت المستغرق: **{elapsed_time:.1f}** ثانية\n"
            f"⚡ المعدل: **{copied_count/elapsed_time:.1f}** رسالة/ثانية"
        )

        await progress_msg.edit(success_msg)

    except Exception as e:
        error_msg = f"❌ حدث خطأ أثناء نسخ الرسائل: {str(e)}"
        print(error_msg)
        try:
            await progress_msg.edit(error_msg)
        except:
            await event.reply(error_msg)

async def main():
    """دالة رئيسية لتشغيل البوت مع معالجة أخطاء تيليجرام"""
    try:
        # تسجيل الدخول كبوت عام
        print("🔐 جاري تسجيل الدخول...")
        await client.start(bot_token=BOT_TOKEN)
        print("✅ تم تسجيل دخول البوت بنجاح!")

    except Exception as e:
        error_str = str(e)

        # معالجة خطأ تحديد معدل الطلبات
        if "A wait of" in error_str and "seconds is required" in error_str:
            # استخراج عدد الثواني من رسالة الخطأ
            import re
            wait_match = re.search(r'A wait of (\d+) seconds is required', error_str)
            if wait_match:
                wait_seconds = int(wait_match.group(1))
                wait_minutes = wait_seconds // 60
                wait_hours = wait_minutes // 60

                if wait_hours > 0:
                    time_str = f"{wait_hours} ساعة و {wait_minutes % 60} دقيقة"
                elif wait_minutes > 0:
                    time_str = f"{wait_minutes} دقيقة و {wait_seconds % 60} ثانية"
                else:
                    time_str = f"{wait_seconds} ثانية"

                print(f"⏳ تحديد معدل الطلبات من تيليجرام!")
                print(f"🕐 يجب الانتظار: {time_str}")
                print(f"💡 نصيحة: استخدم جلسة مختلفة أو انتظر حتى انتهاء المدة")
                print(f"🔄 سيتم إعادة المحاولة تلقائياً...")

                # انتظار مع عداد تنازلي
                for remaining in range(wait_seconds, 0, -1):
                    mins, secs = divmod(remaining, 60)
                    hours, mins = divmod(mins, 60)

                    if hours > 0:
                        time_format = f"{hours:02d}:{mins:02d}:{secs:02d}"
                    else:
                        time_format = f"{mins:02d}:{secs:02d}"

                    print(f"\r⏰ الوقت المتبقي: {time_format}", end="", flush=True)
                    await asyncio.sleep(1)

                print("\n🔄 جاري إعادة المحاولة...")
                await client.start(bot_token=BOT_TOKEN)
                print("✅ تم تسجيل الدخول بنجاح بعد الانتظار!")
        else:
            # أخطاء أخرى
            print(f"❌ خطأ في تسجيل الدخول: {error_str}")
            raise

    @client.on(events.NewMessage)
    async def handle_messages(event):
        try:
            message = event.message
            text = message.text
            user_id = event.sender_id

            # تسجيل النشاط
            print(f"📨 رسالة من المستخدم {user_id}: {text[:50]}...")

            # أمر البداية المحسن
            if text and text.startswith("/start"):
                welcome_msg = (
                    "🚀 **بوت نسخ الرسائل المحسن**\n\n"
                    "✨ **الميزات الجديدة:**\n"
                    "📋 نسخ من القنوات العامة والخاصة\n"
                    "⚡ معالجة سريعة ومحسنة\n"
                    "📱 دعم جميع أنواع الملفات\n"
                    "📊 إحصائيات مفصلة\n"
                    "🔄 شريط تقدم للعمليات\n"
                    "🛡️ حماية من الملفات الكبيرة\n\n"
                    "📖 **الأوامر المتاحة:**\n"
                    "• `/copy <رابط> [عدد]` - نسخ الرسائل\n"
                    "• `/help` - عرض المساعدة\n"
                    "• `/stats` - عرض الإحصائيات\n"
                )

                if is_admin(user_id):
                    welcome_msg += "\n� **أوامر المشرفين:**\n• `/admin` - لوحة الإدارة"

                welcome_msg += (
                    "\n\n�🔗 **أمثلة الاستخدام:**\n"
                    "• `https://t.me/channel_name`\n"
                    "• `https://t.me/c/123456789`\n"
                    "• `@channel_name`\n\n"
                    "📝 **ملاحظة:** الحد الأقصى للمستخدمين العاديين 200 رسالة"
                )

                await event.reply(welcome_msg)
                return

            # أمر المساعدة
            elif text and text.startswith("/help"):
                help_msg = (
                    "📖 **دليل الاستخدام**\n\n"
                    "🔸 **نسخ الرسائل:**\n"
                    "`/copy https://t.me/channel_name 50`\n"
                    "ينسخ آخر 50 رسالة من القناة\n\n"
                    "🔸 **نسخ رسالة واحدة:**\n"
                    "أرسل رابط الرسالة مباشرة\n\n"
                    "🔸 **الحدود:**\n"
                    "• المستخدمون العاديون: 200 رسالة كحد أقصى\n"
                    f"• حجم الملف الأقصى: {MAX_FILE_SIZE_MB}MB\n"
                    f"• التأخير بين الرسائل: {DELAY_BETWEEN_MESSAGES}s\n\n"
                    "❓ للمزيد من المساعدة، تواصل مع المطور"
                )
                await event.reply(help_msg)
                return

            # أمر الإحصائيات (للمشرفين فقط)
            elif text and text.startswith("/stats") and is_admin(user_id):
                stats_msg = (
                    "📊 **إحصائيات البوت**\n\n"
                    f"⏰ وقت التشغيل: منذ بدء الجلسة\n"
                    f"👥 عدد المشرفين: {len(ADMIN_IDS)}\n"
                    f"⚙️ الإعدادات الحالية:\n"
                    f"• الحد الافتراضي: {DEFAULT_LIMIT}\n"
                    f"• التأخير: {DELAY_BETWEEN_MESSAGES}s\n"
                    f"• حجم الملف الأقصى: {MAX_FILE_SIZE_MB}MB\n"
                    f"• عدد المحاولات: {MAX_RETRIES}"
                )
                await event.reply(stats_msg)
                return

            # أمر الإدارة (للمشرفين فقط)
            elif text and text.startswith("/admin") and is_admin(user_id):
                admin_msg = (
                    "🔧 **لوحة الإدارة**\n\n"
                    "الأوامر المتاحة:\n"
                    "• `/stats` - عرض الإحصائيات\n"
                    "• `/broadcast <رسالة>` - إرسال رسالة جماعية\n"
                    "• `/restart` - إعادة تشغيل البوت\n\n"
                    "⚠️ استخدم هذه الأوامر بحذر"
                )
                await event.reply(admin_msg)
                return

            # أمر النسخ المحسن
            elif text and text.startswith("/copy"):
                parts = text.split()
                if len(parts) < 2:
                    await event.reply(
                        "❌ **خطأ في الأمر**\n\n"
                        "الاستخدام الصحيح:\n"
                        "`/copy <رابط_القناة> [عدد_الرسائل]`\n\n"
                        "مثال:\n"
                        "`/copy https://t.me/channel_name 50`"
                    )
                    return

                channel_link = parts[1]
                try:
                    limit = int(parts[2]) if len(parts) > 2 else DEFAULT_LIMIT
                    if limit <= 0:
                        await event.reply("❌ عدد الرسائل يجب أن يكون أكبر من صفر")
                        return
                except ValueError:
                    await event.reply("❌ عدد الرسائل يجب أن يكون رقماً صحيحاً")
                    return

                await copy_channel_messages(event, channel_link, limit)

            # نسخ رسالة واحدة من رابط مباشر
            elif text and 't.me/' in text and '/s/' not in text:
                try:
                    # استخراج معلومات الرسالة من الرابط
                    if '/c/' in text:
                        # رابط قناة خاصة
                        parts = text.split('/')
                        channel_id = parts[-2]
                        message_id = int(parts[-1])
                        channel = int(f"-100{channel_id}")
                    else:
                        # رابط قناة عامة
                        parts = text.split('/')
                        channel = parts[-2]
                        message_id = int(parts[-1])

                    # جلب الرسالة
                    msg = await client.get_messages(channel, ids=message_id)
                    if msg:
                        await client.send_message(
                            event.chat_id,
                            message=msg,
                            formatting_entities=msg.entities
                        )
                        await event.reply("✅ تم نسخ الرسالة بنجاح!")
                    else:
                        await event.reply("❌ لم يتم العثور على الرسالة")

                except (ValueError, IndexError):
                    await event.reply("❌ رابط الرسالة غير صحيح")
                except Exception as e:
                    await event.reply(f"❌ حدث خطأ في جلب الرسالة: {str(e)}")

            # رسالة افتراضية للرسائل غير المفهومة
            elif text and not text.startswith('/'):
                await event.reply(
                    "🤔 لم أفهم طلبك\n\n"
                    "استخدم `/help` لعرض الأوامر المتاحة\n"
                    "أو `/start` للعودة للقائمة الرئيسية"
                )

        except Exception as e:
            error_msg = f"❌ حدث خطأ في معالجة الطلب: {str(e)}"
            print(f"خطأ في معالجة الرسالة: {str(e)}")
            await event.reply(error_msg)

    print("👂 البوت المحسن يعمل ويستمع للأوامر...")
    print(f"🔧 الإعدادات: حد افتراضي={DEFAULT_LIMIT}, تأخير={DELAY_BETWEEN_MESSAGES}s")
    print(f"👥 عدد المشرفين: {len(ADMIN_IDS)}")

    try:
        await client.run_until_disconnected()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
    finally:
        print("🔄 جاري إغلاق البوت...")
        await client.disconnect()

if __name__ == '__main__':
    print("🚀 بدء تشغيل بوت نسخ الرسائل المحسن...")
    print("📝 للإيقاف اضغط Ctrl+C")
    print("💡 في حالة ظهور خطأ Rate Limit، سيتم الانتظار تلقائياً")
    print("-" * 50)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف البوت بنجاح")
    except Exception as e:
        error_str = str(e)
        if "A wait of" in error_str:
            print(f"\n⏳ خطأ تحديد معدل الطلبات: {e}")
            print("💡 نصائح لتجنب هذه المشكلة:")
            print("   • استخدم VPN مختلف")
            print("   • انتظر بعض الوقت قبل إعادة التشغيل")
            print("   • احذف ملفات الجلسة (.session) وأعد المحاولة")
        else:
            print(f"❌ خطأ في بدء التشغيل: {e}")
        exit(1)