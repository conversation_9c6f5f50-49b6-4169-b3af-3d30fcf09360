# 🚀 بوت تيليجرام المحسن لنسخ الرسائل

## ✨ التحسينات الجديدة

### 🔧 معالجة الأخطاء المحسنة
- ✅ معالجة تلقائية لخطأ Rate Limiting مع عداد تنازلي
- ✅ رسائل خطأ واضحة ومفيدة
- ✅ إعادة محاولة تلقائية بعد الانتظار

### 📊 ميزات محسنة
- ✅ شريط تقدم للعمليات الطويلة
- ✅ إحصائيات مفصلة (الوقت، الحجم، المعدل)
- ✅ دعم أفضل لجميع أنواع الملفات
- ✅ حماية من الملفات الكبيرة
- ✅ تنظيف تلقائي للملفات المؤقتة

### 🛡️ الأمان والتحكم
- ✅ نظام صلاحيات للمشرفين
- ✅ حدود مختلفة للمستخدمين العاديين والمشرفين
- ✅ تحكم في حجم الملفات المسموح بها
- ✅ جلسات فريدة لتجنب التعارض

### 📱 واجهة المستخدم المحسنة
- ✅ أوامر محسنة مع أمثلة واضحة
- ✅ رسائل مساعدة تفصيلية
- ✅ دعم أشكال مختلفة من روابط القنوات
- ✅ تنسيق أفضل للرسائل

## 🚀 التشغيل السريع

### 1. تثبيت المتطلبات
```bash
pip install telethon python-dotenv
```

### 2. إعداد ملف البيئة
تأكد من وجود ملف `.env` مع بياناتك:
```env
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
BOT_TOKEN=your_bot_token
```

### 3. تشغيل البوت
```bash
python converted_bot.py
```

## 🔧 حل مشكلة Rate Limiting

إذا ظهرت رسالة خطأ مثل:
```
❌ A wait of 477 seconds is required (caused by ImportBotAuthorizationRequest)
```

### الحلول المتاحة:

#### 1. الانتظار التلقائي ⏳
البوت الآن يتعامل مع هذا الخطأ تلقائياً:
- يعرض الوقت المتبقي بعداد تنازلي
- ينتظر المدة المطلوبة
- يعيد المحاولة تلقائياً

#### 2. إعادة تعيين الجلسة 🔄
```bash
python reset_session.py
```
هذا سيحذف ملفات الجلسة القديمة

#### 3. حلول إضافية 💡
- استخدم VPN أو شبكة مختلفة
- انتظر بعض الوقت قبل إعادة التشغيل
- تأكد من عدم تشغيل عدة نسخ من البوت

## 📖 دليل الاستخدام

### الأوامر الأساسية

#### `/start`
عرض الرسالة الترحيبية والميزات المتاحة

#### `/help`
عرض دليل الاستخدام التفصيلي

#### `/copy <رابط> [عدد]`
نسخ الرسائل من قناة
```
/copy https://t.me/channel_name 50
/copy https://t.me/c/123456789 100
/copy @channel_name 25
```

### أوامر المشرفين

#### `/stats`
عرض إحصائيات البوت (للمشرفين فقط)

#### `/admin`
عرض لوحة الإدارة (للمشرفين فقط)

### نسخ رسالة واحدة
أرسل رابط الرسالة مباشرة:
```
https://t.me/channel_name/123
https://t.me/c/123456789/456
```

## ⚙️ الإعدادات المتقدمة

### حدود المستخدمين
- **المستخدمون العاديون**: 200 رسالة كحد أقصى
- **المشرفون**: بدون حدود
- **حجم الملف الأقصى**: قابل للتخصيص (افتراضي 2GB)

### الأداء
- **التأخير بين الرسائل**: قابل للتخصيص (افتراضي 0.5 ثانية)
- **عدد المحاولات**: قابل للتخصيص (افتراضي 3 محاولات)
- **تنظيف تلقائي**: للملفات المؤقتة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### "❌ رابط القناة غير صحيح"
- تأكد من صحة رابط القناة
- الأشكال المدعومة: `https://t.me/channel`, `https://t.me/c/123`, `@channel`

#### "❌ لم يتم العثور على رسائل"
- تأكد من أن القناة عامة أو أن البوت عضو فيها
- تحقق من وجود رسائل في القناة

#### "❌ حجم الملف كبير جداً"
- قم بتعديل `BOT_MAX_FILE_SIZE_MB` في ملف `.env`
- أو اطلب من المشرف زيادة الحد

## 📊 الإحصائيات والمراقبة

البوت يوفر إحصائيات مفصلة تشمل:
- عدد الرسائل المنسوخة
- عدد الرسائل الفاشلة
- إجمالي حجم الملفات
- الوقت المستغرق
- معدل النسخ

## 🔒 الأمان

### نصائح الأمان
- لا تشارك ملف `.env` مع أحد
- استخدم حساب بوت منفصل
- راقب استخدام البوت بانتظام
- حدد صلاحيات المشرفين بعناية

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود
- تحسين الوثائق

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- افتح issue في GitHub
- تواصل مع المطور
- راجع الوثائق

---

**تم التطوير بـ ❤️ للمجتمع العربي**
