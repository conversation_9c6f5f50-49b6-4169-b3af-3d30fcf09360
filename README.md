# 🚀 بوت تيليجرام المحسن لنسخ الرسائل

## 📋 الوصف
بوت تيليجرام محسن وقوي لنسخ الرسائل من القنوات العامة والخاصة مع ميزات متقدمة وواجهة مستخدم محسنة.

## ✨ الميزات الجديدة

### 🔧 التحسينات الأساسية
- ✅ معالجة أخطاء محسنة مع رسائل واضحة
- ✅ شريط تقدم للعمليات الطويلة
- ✅ إحصائيات مفصلة لكل عملية نسخ
- ✅ دعم أفضل لجميع أنواع الملفات
- ✅ حماية من الملفات الكبيرة
- ✅ تنظيف تلقائي للملفات المؤقتة

### 🛡️ الأمان والتحكم
- ✅ نظام صلاحيات للمشرفين
- ✅ حدود مختلفة للمستخدمين العاديين والمشرفين
- ✅ تحكم في حجم الملفات المسموح بها
- ✅ تحكم في معدل العمليات

### 📱 واجهة المستخدم
- ✅ أوامر محسنة مع أمثلة واضحة
- ✅ رسائل مساعدة تفصيلية
- ✅ دعم أشكال مختلفة من روابط القنوات
- ✅ تنسيق أفضل للرسائل

## 🔧 التثبيت والإعداد

### 1. متطلبات النظام
```bash
Python 3.8+
pip (مدير الحزم)
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد ملف البيئة
انسخ ملف `.env` وأضف بياناتك:

```env
# إعدادات تيليجرام الأساسية
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
BOT_TOKEN=your_bot_token

# إعدادات اختيارية
BOT_MAX_RETRIES=3
BOT_DELAY_BETWEEN_MESSAGES=0.5
BOT_DEFAULT_LIMIT=100
BOT_MAX_FILE_SIZE_MB=2000
ADMIN_USER_IDS=123456789,987654321
```

### 4. تشغيل البوت
```bash
python converted_bot.py
```

## 📖 دليل الاستخدام

### الأوامر الأساسية

#### `/start`
عرض الرسالة الترحيبية والميزات المتاحة

#### `/help`
عرض دليل الاستخدام التفصيلي

#### `/copy <رابط> [عدد]`
نسخ الرسائل من قناة
```
/copy https://t.me/channel_name 50
/copy https://t.me/c/123456789 100
/copy @channel_name 25
```

### أوامر المشرفين

#### `/stats`
عرض إحصائيات البوت (للمشرفين فقط)

#### `/admin`
عرض لوحة الإدارة (للمشرفين فقط)

### نسخ رسالة واحدة
أرسل رابط الرسالة مباشرة:
```
https://t.me/channel_name/123
https://t.me/c/123456789/456
```

## ⚙️ الإعدادات المتقدمة

### حدود المستخدمين
- **المستخدمون العاديون**: 200 رسالة كحد أقصى
- **المشرفون**: بدون حدود
- **حجم الملف الأقصى**: قابل للتخصيص (افتراضي 2GB)

### الأداء
- **التأخير بين الرسائل**: قابل للتخصيص (افتراضي 0.5 ثانية)
- **عدد المحاولات**: قابل للتخصيص (افتراضي 3 محاولات)
- **تنظيف تلقائي**: للملفات المؤقتة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### "❌ رابط القناة غير صحيح"
- تأكد من صحة رابط القناة
- الأشكال المدعومة: `https://t.me/channel`, `https://t.me/c/123`, `@channel`

#### "❌ لم يتم العثور على رسائل"
- تأكد من أن القناة عامة أو أن البوت عضو فيها
- تحقق من وجود رسائل في القناة

#### "❌ حجم الملف كبير جداً"
- قم بتعديل `BOT_MAX_FILE_SIZE_MB` في ملف `.env`
- أو اطلب من المشرف زيادة الحد

## 📊 الإحصائيات والمراقبة

البوت يوفر إحصائيات مفصلة تشمل:
- عدد الرسائل المنسوخة
- عدد الرسائل الفاشلة
- إجمالي حجم الملفات
- الوقت المستغرق
- معدل النسخ

## 🔒 الأمان

### نصائح الأمان
- لا تشارك ملف `.env` مع أحد
- استخدم حساب بوت منفصل
- راقب استخدام البوت بانتظام
- حدد صلاحيات المشرفين بعناية

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود
- تحسين الوثائق

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- افتح issue في GitHub
- تواصل مع المطور
- راجع الوثائق

---

**تم التطوير بـ ❤️ للمجتمع العربي**
